"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Eye, EyeOff, UserPlus, AlertCircle, Loader2, Check } from "lucide-react"
import { useAuthStore } from "@/stores/user-store"
import { useToast } from "@/hooks/use-toast"

export default function RegisterPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { register, isAuthenticated, isLoading } = useAuthStore()
  
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    first_name: "",
    last_name: "",
    password: "",
    password_confirmation: ""
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [errors, setErrors] = useState<{ field: string; message: string }[]>([])
  const [generalError, setGeneralError] = useState("")

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push("/")
    }
  }, [isAuthenticated, router])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([])
      setGeneralError("")
    }
  }

  const validateForm = () => {
    const newErrors: { field: string; message: string }[] = []

    if (!formData.username.trim()) {
      newErrors.push({ field: "username", message: "Username is required" })
    } else if (formData.username.length < 3) {
      newErrors.push({ field: "username", message: "Username must be at least 3 characters" })
    } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.username)) {
      newErrors.push({ field: "username", message: "Username can only contain letters, numbers, underscores, and hyphens" })
    }

    if (!formData.email.trim()) {
      newErrors.push({ field: "email", message: "Email is required" })
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.push({ field: "email", message: "Please enter a valid email address" })
    }

    if (!formData.first_name.trim()) {
      newErrors.push({ field: "first_name", message: "First name is required" })
    }

    if (!formData.last_name.trim()) {
      newErrors.push({ field: "last_name", message: "Last name is required" })
    }

    if (!formData.password) {
      newErrors.push({ field: "password", message: "Password is required" })
    } else if (formData.password.length < 8) {
      newErrors.push({ field: "password", message: "Password must be at least 8 characters" })
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.push({ field: "password", message: "Password must contain at least one lowercase letter, one uppercase letter, and one number" })
    }

    if (!formData.password_confirmation) {
      newErrors.push({ field: "password_confirmation", message: "Please confirm your password" })
    } else if (formData.password !== formData.password_confirmation) {
      newErrors.push({ field: "password_confirmation", message: "Passwords don't match" })
    }

    return newErrors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors([])
    setGeneralError("")

    // Validate form
    const validationErrors = validateForm()
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Registration Successful",
          description: result.message || "Your account has been created. Please log in.",
        })
        router.push("/auth/login")
      } else {
        if (result.errors && result.errors.length > 0) {
          setErrors(result.errors)
        } else {
          setGeneralError(result.message || "Registration failed")
        }
      }
    } catch (error) {
      console.error("Registration error:", error)
      setGeneralError("An unexpected error occurred. Please try again.")
    }
  }

  const getFieldError = (field: string) => {
    return errors.find(error => error.field === field)?.message
  }

  const getPasswordStrength = () => {
    const password = formData.password
    if (!password) return { strength: 0, text: "" }
    
    let strength = 0
    const checks = [
      password.length >= 8,
      /[a-z]/.test(password),
      /[A-Z]/.test(password),
      /\d/.test(password),
      /[!@#$%^&*(),.?":{}|<>]/.test(password)
    ]
    
    strength = checks.filter(Boolean).length
    
    const strengthTexts = ["Very Weak", "Weak", "Fair", "Good", "Strong"]
    const strengthColors = ["text-red-500", "text-orange-500", "text-yellow-500", "text-blue-500", "text-green-500"]
    
    return {
      strength,
      text: strengthTexts[strength - 1] || "",
      color: strengthColors[strength - 1] || ""
    }
  }

  if (isAuthenticated) {
    return null // Will redirect
  }

  const passwordStrength = getPasswordStrength()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background/95 to-background/90 p-4">
      <div className="w-full max-w-md">
        <Card className="glass-ultra border border-border shadow-glass">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] bg-clip-text text-transparent">
              Create Account
            </CardTitle>
            <CardDescription>
              Join CythroDash and start managing your servers
            </CardDescription>
          </CardHeader>
          
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {generalError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{generalError}</AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    name="first_name"
                    type="text"
                    placeholder="John"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    className={getFieldError("first_name") ? "border-red-500" : ""}
                    disabled={isLoading}
                  />
                  {getFieldError("first_name") && (
                    <p className="text-sm text-red-500">{getFieldError("first_name")}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    name="last_name"
                    type="text"
                    placeholder="Doe"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    className={getFieldError("last_name") ? "border-red-500" : ""}
                    disabled={isLoading}
                  />
                  {getFieldError("last_name") && (
                    <p className="text-sm text-red-500">{getFieldError("last_name")}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  type="text"
                  placeholder="johndoe"
                  value={formData.username}
                  onChange={handleInputChange}
                  className={getFieldError("username") ? "border-red-500" : ""}
                  disabled={isLoading}
                />
                {getFieldError("username") && (
                  <p className="text-sm text-red-500">{getFieldError("username")}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={getFieldError("email") ? "border-red-500" : ""}
                  disabled={isLoading}
                />
                {getFieldError("email") && (
                  <p className="text-sm text-red-500">{getFieldError("email")}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Create a strong password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={getFieldError("password") ? "border-red-500 pr-10" : "pr-10"}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                {formData.password && (
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-muted rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all ${
                          passwordStrength.strength <= 2 ? 'bg-red-500' :
                          passwordStrength.strength <= 3 ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`}
                        style={{ width: `${(passwordStrength.strength / 5) * 100}%` }}
                      />
                    </div>
                    <span className={`text-xs ${passwordStrength.color}`}>
                      {passwordStrength.text}
                    </span>
                  </div>
                )}
                {getFieldError("password") && (
                  <p className="text-sm text-red-500">{getFieldError("password")}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password_confirmation">Confirm Password</Label>
                <div className="relative">
                  <Input
                    id="password_confirmation"
                    name="password_confirmation"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    value={formData.password_confirmation}
                    onChange={handleInputChange}
                    className={getFieldError("password_confirmation") ? "border-red-500 pr-10" : "pr-10"}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={isLoading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                {formData.password_confirmation && formData.password === formData.password_confirmation && (
                  <div className="flex items-center gap-1 text-green-500">
                    <Check className="h-3 w-3" />
                    <span className="text-xs">Passwords match</span>
                  </div>
                )}
                {getFieldError("password_confirmation") && (
                  <p className="text-sm text-red-500">{getFieldError("password_confirmation")}</p>
                )}
              </div>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button 
                type="submit" 
                className="w-full glass-button bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] hover:from-[rgb(20,136,204)]/90 hover:to-[rgb(43,50,178)]/90"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating account...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Create Account
                  </>
                )}
              </Button>
              
              <p className="text-center text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link 
                  href="/auth/login" 
                  className="text-brand hover:text-brand/80 transition-colors font-medium"
                >
                  Sign in
                </Link>
              </p>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}
