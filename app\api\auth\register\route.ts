/**
 * CythroDash - Register API Route
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { RegisterController } from '@/hooks/managers/controller/Auth/Register';
import { z } from 'zod';
import { SECURITY_CONFIG, getSessionCookieOptions, getClientIP, validatePassword } from '../../../../lib/security/config';

// Input validation schema
const registerSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be at most 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be at most 255 characters'),
  first_name: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be at most 50 characters'),
  last_name: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be at most 50 characters'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be at most 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  password_confirmation: z.string()
}).refine((data) => data.password === data.password_confirmation, {
  message: "Passwords don't match",
  path: ["password_confirmation"],
});

// Rate limiting for registration
const registrationAttempts = new Map<string, { count: number; lastAttempt: number }>();

function checkRegistrationRateLimit(ip: string): boolean {
  const now = Date.now();
  const attempts = registrationAttempts.get(ip);
  const config = SECURITY_CONFIG.RATE_LIMIT.REGISTER;

  if (!attempts) {
    registrationAttempts.set(ip, { count: 1, lastAttempt: now });
    return true;
  }

  // Reset if lockout period has passed
  if (now - attempts.lastAttempt > config.LOCKOUT_DURATION) {
    registrationAttempts.set(ip, { count: 1, lastAttempt: now });
    return true;
  }

  // Check if too many attempts
  if (attempts.count >= config.MAX_ATTEMPTS) {
    return false;
  }

  // Increment attempts
  attempts.count++;
  attempts.lastAttempt = now;
  return true;
}

function resetRegistrationRateLimit(ip: string): void {
  registrationAttempts.delete(ip);
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP
    const ip = getClientIP(request);

    // Check rate limiting
    if (!checkRegistrationRateLimit(ip)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Too many registration attempts. Please try again later.',
          error: 'RATE_LIMITED'
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const inputValidation = registerSchema.safeParse(body);

    if (!inputValidation.success) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid input data',
          errors: inputValidation.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    const { username, email, first_name, last_name, password } = inputValidation.data;

    // Prepare registration request
    const registerRequest = {
      username,
      email,
      first_name,
      last_name,
      password,
      confirm_password: password, // Since we already validated they match
      terms_accepted: true, // Assume accepted for now
      privacy_accepted: true, // Assume accepted for now
      ip_address: ip,
      user_agent: request.headers.get('user-agent') || 'unknown'
    };

    // Attempt registration
    const registerResult = await RegisterController.registerUser(registerRequest);

    if (registerResult.success && registerResult.user) {
      // Reset rate limiting on successful registration
      resetRegistrationRateLimit(ip);

      // Return success response without automatic login
      return NextResponse.json({
        success: true,
        message: registerResult.user.verification_required
          ? 'Registration successful! Please check your email to verify your account.'
          : 'Registration successful! You can now log in.',
        user: {
          id: registerResult.user.id,
          username: registerResult.user.username,
          email: registerResult.user.email,
          verified: registerResult.user.verified,
          verification_required: registerResult.user.verification_required
        }
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: registerResult.message || 'Registration failed',
          errors: registerResult.errors || []
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Registration API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      ...SECURITY_CONFIG.HEADERS
    },
  });
}
