"use client"

import { useState } from "react"
import PanelLayout from "@/components/panel-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Send,
  User,
  Coins,
  ArrowRight,
  Shield,
  Zap,
  CheckCircle,
  AlertTriangle,
  Users,
  History,
  Gift,
} from "lucide-react"
import { useWalletStore } from "@/stores/wallet-store"
import { useToast } from "@/hooks/use-toast"

export default function TransferPage() {
  const [recipient, setRecipient] = useState("")
  const [amount, setAmount] = useState("")
  const [note, setNote] = useState("")
  const { balance, removeCoins } = useWalletStore()
  const { toast } = useToast()

  const quickAmounts = [10, 25, 50, 100]

  const recentTransfers = [
    { id: 1, to: "john_doe", amount: 50, date: "2024-01-15", status: "completed" },
    { id: 2, to: "jane_smith", amount: 25, date: "2024-01-14", status: "completed" },
    { id: 3, to: "mike_wilson", amount: 100, date: "2024-01-13", status: "pending" },
  ]

  const handleTransfer = () => {
    const transferAmount = Number.parseInt(amount)

    if (!recipient.trim()) {
      toast({
        title: "Missing Recipient",
        description: "Please enter a username to transfer coins to.",
        variant: "destructive",
      })
      return
    }

    if (!transferAmount || transferAmount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid amount to transfer.",
        variant: "destructive",
      })
      return
    }

    if (transferAmount > balance) {
      toast({
        title: "Insufficient Balance",
        description: `You only have ${balance} coins available.`,
        variant: "destructive",
      })
      return
    }

    removeCoins(transferAmount, `Transferred to ${recipient}${note ? ` - ${note}` : ""}`)

    toast({
      title: "Transfer Successful! 🎉",
      description: `Successfully transferred ${transferAmount} coins to ${recipient}.`,
    })

    // Reset form
    setRecipient("")
    setAmount("")
    setNote("")
  }

  const handleQuickAmount = (quickAmount: number) => {
    setAmount(quickAmount.toString())
  }

  const handleMaxAmount = () => {
    setAmount(balance.toString())
  }

  return (
    <PanelLayout title="Transfer Coins" subtitle="Send coins to other users instantly and securely">
      <div className="space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)]/10 via-[rgb(32,141,209)]/8 to-[rgb(43,50,178)]/10 border border-[rgb(20,136,204)]/20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent" />
          <div className="relative p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 border border-[rgb(20,136,204)]/30">
                  <Send className="w-4 h-4 text-[rgb(20,136,204)]" />
                  <span className="text-sm font-medium text-foreground">Instant Transfers</span>
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-black brand-heading mb-4">Transfer Coins</h1>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Send coins to friends, team members, or other users instantly. All transfers are secure, instant,
                    and completely free with no hidden fees.
                  </p>
                </div>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Zap className="w-5 h-5 text-blue-400" />
                    <span className="text-sm font-medium">Instant Transfer</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Shield className="w-5 h-5 text-emerald-400" />
                    <span className="text-sm font-medium">100% Secure</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Gift className="w-5 h-5 text-purple-400" />
                    <span className="text-sm font-medium">No Fees</span>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-32 h-32 rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] shadow-2xl shadow-[rgb(20,136,204)]/25 mb-6">
                  <Send className="w-16 h-16 text-white" />
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Available Balance</p>
                  <div className="flex items-center justify-center gap-2">
                    <Coins className="w-6 h-6 text-[rgb(20,136,204)]" />
                    <p className="text-4xl font-black text-foreground">{balance}</p>
                  </div>
                  <p className="text-lg text-muted-foreground">coins</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Transfer Form */}
          <div className="lg:col-span-2">
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                    <Send className="w-5 h-5 text-white" />
                  </div>
                  Send Coins
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Recipient */}
                <div className="space-y-2">
                  <Label htmlFor="recipient" className="text-base font-semibold">
                    Recipient Username
                  </Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                    <Input
                      id="recipient"
                      placeholder="Enter username"
                      value={recipient}
                      onChange={(e) => setRecipient(e.target.value)}
                      className="glass-input pl-10 h-12"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Enter the exact username of the person you want to send coins to
                  </p>
                </div>

                {/* Amount */}
                <div className="space-y-4">
                  <Label htmlFor="amount" className="text-base font-semibold">
                    Amount
                  </Label>
                  <div className="relative">
                    <Coins className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      className="glass-input pl-10 h-12"
                      min="1"
                      max={balance}
                    />
                  </div>

                  {/* Quick Amount Buttons */}
                  <div className="space-y-3">
                    <p className="text-sm font-medium text-foreground">Quick amounts:</p>
                    <div className="flex flex-wrap gap-2">
                      {quickAmounts.map((quickAmount) => (
                        <Button
                          key={quickAmount}
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAmount(quickAmount)}
                          className="secondary-button bg-transparent"
                          disabled={quickAmount > balance}
                        >
                          {quickAmount}
                        </Button>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleMaxAmount}
                        className="secondary-button bg-transparent"
                        disabled={balance === 0}
                      >
                        Max ({balance})
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Note */}
                <div className="space-y-2">
                  <Label htmlFor="note" className="text-base font-semibold">
                    Note (Optional)
                  </Label>
                  <Input
                    id="note"
                    placeholder="Add a message..."
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                    className="glass-input h-12"
                    maxLength={100}
                  />
                  <p className="text-sm text-muted-foreground">Add a personal message with your transfer</p>
                </div>

                {/* Transfer Summary */}
                {amount && recipient && (
                  <div className="p-4 glass-ultra rounded-xl border border-border/50 bg-gradient-to-br from-background/50 to-background/30">
                    <h4 className="font-semibold text-foreground mb-3">Transfer Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">To:</span>
                        <span className="text-foreground font-medium">{recipient}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Amount:</span>
                        <div className="flex items-center gap-1">
                          <Coins className="w-4 h-4 text-[rgb(20,136,204)]" />
                          <span className="text-foreground font-medium">{amount} coins</span>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Fee:</span>
                        <span className="text-emerald-400 font-medium">FREE</span>
                      </div>
                      {note && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Note:</span>
                          <span className="text-foreground font-medium">{note}</span>
                        </div>
                      )}
                      <div className="border-t border-border/50 pt-2 mt-3">
                        <div className="flex justify-between">
                          <span className="text-foreground font-semibold">Remaining Balance:</span>
                          <div className="flex items-center gap-1">
                            <Coins className="w-4 h-4 text-[rgb(20,136,204)]" />
                            <span className="text-foreground font-bold">
                              {balance - Number.parseInt(amount || "0")} coins
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Transfer Button */}
                <Button
                  onClick={handleTransfer}
                  disabled={
                    !recipient ||
                    !amount ||
                    Number.parseInt(amount || "0") > balance ||
                    Number.parseInt(amount || "0") <= 0
                  }
                  className="premium-button w-full h-12"
                >
                  <Send className="w-5 h-5 mr-2" />
                  Send {amount} Coins
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Transfer Tips */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
                    <AlertTriangle className="w-4 h-4 text-white" />
                  </div>
                  Transfer Tips
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
                    <p className="text-muted-foreground">Double-check the username before sending</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
                    <p className="text-muted-foreground">Transfers are instant and cannot be reversed</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
                    <p className="text-muted-foreground">No fees for any transfer amount</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
                    <p className="text-muted-foreground">Minimum transfer amount is 1 coin</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Transfers */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                    <History className="w-4 h-4 text-white" />
                  </div>
                  Recent Transfers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentTransfers.map((transfer) => (
                    <div
                      key={transfer.id}
                      className="flex items-center justify-between p-3 glass-ultra rounded-lg border border-border/50"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                          <Users className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-foreground">{transfer.to}</p>
                          <p className="text-xs text-muted-foreground">{transfer.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-1">
                          <Coins className="w-3 h-3 text-[rgb(20,136,204)]" />
                          <span className="text-sm font-bold text-foreground">{transfer.amount}</span>
                        </div>
                        <Badge
                          className={
                            transfer.status === "completed"
                              ? "bg-emerald-400/10 text-emerald-400 border-emerald-400/20"
                              : "bg-yellow-400/10 text-yellow-400 border-yellow-400/20"
                          }
                        >
                          {transfer.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PanelLayout>
  )
}
