"use client"

import { useState } from "react"
import PanelLayout from "@/components/panel-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  Share2,
  Copy,
  Gift,
  Trophy,
  Star,
  Crown,
  Gem,
  TrendingUp,
  UserPlus,
  Coins,
  Link2,
  MessageSquare,
  Mail,
  CheckCircle,
  Zap,
  Target,
  Award,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

const referralTiers = [
  {
    name: "Bronze",
    minReferrals: 0,
    maxReferrals: 4,
    bonus: 10,
    color: "from-amber-600 to-amber-800",
    bgColor: "bg-amber-500/10",
    textColor: "text-amber-400",
    borderColor: "border-amber-500/20",
    icon: Star,
    perks: ["10% bonus on referral rewards", "Basic referral tracking"],
  },
  {
    name: "Silver",
    minReferrals: 5,
    maxReferrals: 14,
    bonus: 25,
    color: "from-gray-400 to-gray-600",
    bgColor: "bg-gray-500/10",
    textColor: "text-gray-400",
    borderColor: "border-gray-500/20",
    icon: Trophy,
    perks: ["25% bonus on referral rewards", "Priority support", "Custom referral code"],
  },
  {
    name: "Gold",
    minReferrals: 15,
    maxReferrals: 49,
    bonus: 50,
    color: "from-yellow-400 to-yellow-600",
    bgColor: "bg-yellow-500/10",
    textColor: "text-yellow-400",
    borderColor: "border-yellow-500/20",
    icon: Crown,
    perks: ["50% bonus on referral rewards", "VIP support", "Exclusive events", "Monthly bonus"],
  },
  {
    name: "Diamond",
    minReferrals: 50,
    maxReferrals: Number.POSITIVE_INFINITY,
    bonus: 100,
    color: "from-cyan-400 to-blue-600",
    bgColor: "bg-cyan-500/10",
    textColor: "text-cyan-400",
    borderColor: "border-cyan-500/20",
    icon: Gem,
    perks: ["100% bonus on referral rewards", "Personal account manager", "Beta access", "Revenue sharing"],
  },
]

const recentReferrals = [
  { id: 1, username: "john_doe", joined: "2024-01-15", earned: 50, status: "active" },
  { id: 2, username: "jane_smith", joined: "2024-01-14", earned: 25, status: "active" },
  { id: 3, username: "mike_wilson", joined: "2024-01-13", earned: 75, status: "active" },
  { id: 4, username: "sarah_jones", joined: "2024-01-12", earned: 30, status: "pending" },
  { id: 5, username: "alex_brown", joined: "2024-01-11", earned: 60, status: "active" },
]

export default function ReferralsPage() {
  const [referralCode] = useState("PTERODASH_USER123")
  const { toast } = useToast()

  // Mock data
  const totalReferrals = 12
  const totalEarned = 1250
  const thisMonthEarned = 340
  const currentTier =
    referralTiers.find((tier) => totalReferrals >= tier.minReferrals && totalReferrals <= tier.maxReferrals) ||
    referralTiers[0]
  const nextTier = referralTiers.find((tier) => tier.minReferrals > totalReferrals)

  const copyReferralCode = () => {
    navigator.clipboard.writeText(referralCode)
    toast({
      title: "Copied! 📋",
      description: "Referral code copied to clipboard.",
    })
  }

  const copyReferralLink = () => {
    const link = `https://pterodash.com/register?ref=${referralCode}`
    navigator.clipboard.writeText(link)
    toast({
      title: "Copied! 🔗",
      description: "Referral link copied to clipboard.",
    })
  }

  const shareOnSocial = (platform: string) => {
    const link = `https://pterodash.com/register?ref=${referralCode}`
    const text = "Join me on Pterodash - the best game server hosting platform! Use my referral code for bonus coins."

    let shareUrl = ""
    switch (platform) {
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(link)}`
        break
      case "discord":
        // Discord doesn't have direct sharing, so just copy
        navigator.clipboard.writeText(`${text} ${link}`)
        toast({ title: "Copied for Discord!", description: "Message copied to clipboard." })
        return
      case "email":
        shareUrl = `mailto:?subject=Join Pterodash&body=${encodeURIComponent(`${text}\n\n${link}`)}`
        break
    }

    if (shareUrl) {
      window.open(shareUrl, "_blank")
    }
  }

  return (
    <PanelLayout title="Referral Program" subtitle="Invite friends and earn coins together">
      <div className="space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)]/10 via-[rgb(32,141,209)]/8 to-[rgb(43,50,178)]/10 border border-[rgb(20,136,204)]/20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent" />
          <div className="relative p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 border border-[rgb(20,136,204)]/30">
                  <Users className="w-4 h-4 text-[rgb(20,136,204)]" />
                  <span className="text-sm font-medium text-foreground">Invite & Earn</span>
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-black brand-heading mb-4">Referral Program</h1>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Invite your friends to join Pterodash and earn coins for every successful referral. The more friends
                    you invite, the higher your tier and the bigger your rewards!
                  </p>
                </div>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Gift className="w-5 h-5 text-emerald-400" />
                    <span className="text-sm font-medium">50 Coins per Referral</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Trophy className="w-5 h-5 text-amber-400" />
                    <span className="text-sm font-medium">Tier Bonuses</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Zap className="w-5 h-5 text-blue-400" />
                    <span className="text-sm font-medium">Instant Rewards</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-black text-foreground">{totalReferrals}</p>
                  <p className="text-sm text-muted-foreground">Total Referrals</p>
                </div>
                <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
                    <Coins className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-black text-foreground">{totalEarned}</p>
                  <p className="text-sm text-muted-foreground">Total Earned</p>
                </div>
                <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
                  <div
                    className={`w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br ${currentTier.color} flex items-center justify-center`}
                  >
                    <currentTier.icon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-black text-foreground">{currentTier.name}</p>
                  <p className="text-sm text-muted-foreground">Current Tier</p>
                </div>
                <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-black text-foreground">{thisMonthEarned}</p>
                  <p className="text-sm text-muted-foreground">This Month</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Referral Code */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                    <Share2 className="w-5 h-5 text-white" />
                  </div>
                  Your Referral Code
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex gap-3">
                    <Input value={referralCode} readOnly className="glass-input h-12 font-mono text-lg" />
                    <Button onClick={copyReferralCode} className="premium-button h-12 px-6">
                      <Copy className="w-5 h-5 mr-2" />
                      Copy Code
                    </Button>
                  </div>

                  <div className="flex gap-3">
                    <Input
                      value={`https://pterodash.com/register?ref=${referralCode}`}
                      readOnly
                      className="glass-input h-12 text-sm"
                    />
                    <Button
                      onClick={copyReferralLink}
                      variant="outline"
                      className="secondary-button bg-transparent h-12 px-6"
                    >
                      <Link2 className="w-5 h-5 mr-2" />
                      Copy Link
                    </Button>
                  </div>
                </div>

                {/* Share Buttons */}
                <div className="space-y-3">
                  <p className="text-sm font-medium text-foreground">Share on social media:</p>
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={() => shareOnSocial("twitter")}
                      variant="outline"
                      className="secondary-button bg-transparent"
                    >
                      <Share2 className="w-4 h-4 mr-2" />
                      Twitter
                    </Button>
                    <Button
                      onClick={() => shareOnSocial("discord")}
                      variant="outline"
                      className="secondary-button bg-transparent"
                    >
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Discord
                    </Button>
                    <Button
                      onClick={() => shareOnSocial("email")}
                      variant="outline"
                      className="secondary-button bg-transparent"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Email
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* How It Works */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
                    <Target className="w-5 h-5 text-white" />
                  </div>
                  How It Works
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center shadow-lg">
                      <Share2 className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-foreground mb-2">1. Share Your Code</h3>
                      <p className="text-sm text-muted-foreground">
                        Share your unique referral code or link with friends
                      </p>
                    </div>
                  </div>
                  <div className="text-center space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center shadow-lg">
                      <UserPlus className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-foreground mb-2">2. Friend Joins</h3>
                      <p className="text-sm text-muted-foreground">Your friend signs up using your referral code</p>
                    </div>
                  </div>
                  <div className="text-center space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center shadow-lg">
                      <Coins className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-foreground mb-2">3. Earn Rewards</h3>
                      <p className="text-sm text-muted-foreground">Both you and your friend get bonus coins!</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Referrals */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  Recent Referrals
                  <Badge className="bg-purple-400/10 text-purple-400 border-purple-400/20">
                    {recentReferrals.length} this month
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentReferrals.map((referral) => (
                    <div
                      key={referral.id}
                      className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                          <Users className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-foreground">{referral.username}</p>
                          <p className="text-sm text-muted-foreground">Joined {referral.joined}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-1 mb-1">
                          <Coins className="w-4 h-4 text-[rgb(20,136,204)]" />
                          <span className="font-bold text-foreground">+{referral.earned}</span>
                        </div>
                        <Badge
                          className={
                            referral.status === "active"
                              ? "bg-emerald-400/10 text-emerald-400 border-emerald-400/20"
                              : "bg-yellow-400/10 text-yellow-400 border-yellow-400/20"
                          }
                        >
                          {referral.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Current Tier */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div
                    className={`w-6 h-6 rounded-lg bg-gradient-to-br ${currentTier.color} flex items-center justify-center`}
                  >
                    <currentTier.icon className="w-4 h-4 text-white" />
                  </div>
                  {currentTier.name} Tier
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <p className="text-3xl font-black text-foreground mb-1">+{currentTier.bonus}%</p>
                  <p className="text-sm text-muted-foreground">Bonus on all referral rewards</p>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium text-foreground">Tier Benefits:</p>
                  <ul className="space-y-1">
                    {currentTier.perks.map((perk, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                        <CheckCircle className="w-3 h-3 text-emerald-400 flex-shrink-0" />
                        {perk}
                      </li>
                    ))}
                  </ul>
                </div>

                {nextTier && (
                  <div className="space-y-3 pt-4 border-t border-border/50">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-foreground">Next Tier:</p>
                      <Badge className={nextTier.bgColor + " " + nextTier.textColor + " " + nextTier.borderColor}>
                        {nextTier.name}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Progress</span>
                        <span className="text-foreground font-medium">
                          {totalReferrals}/{nextTier.minReferrals}
                        </span>
                      </div>
                      <Progress value={(totalReferrals / nextTier.minReferrals) * 100} className="h-2" />
                      <p className="text-xs text-muted-foreground">
                        {nextTier.minReferrals - totalReferrals} more referrals to unlock {nextTier.name}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* All Tiers */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
                    <Award className="w-4 h-4 text-white" />
                  </div>
                  All Tiers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {referralTiers.map((tier) => {
                    const IconComponent = tier.icon
                    const isCurrentTier = tier.name === currentTier.name
                    const isUnlocked = totalReferrals >= tier.minReferrals

                    return (
                      <div
                        key={tier.name}
                        className={`p-3 rounded-xl border transition-all ${
                          isCurrentTier
                            ? `${tier.bgColor} ${tier.borderColor}`
                            : isUnlocked
                              ? "glass-ultra border-border/50"
                              : "glass-ultra border-border/30 opacity-60"
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div
                              className={`w-6 h-6 rounded-lg bg-gradient-to-br ${tier.color} flex items-center justify-center`}
                            >
                              <IconComponent className="w-3 h-3 text-white" />
                            </div>
                            <span className="font-medium text-foreground">{tier.name}</span>
                            {isCurrentTier && (
                              <Badge className="bg-emerald-400/10 text-emerald-400 border-emerald-400/20 text-xs">
                                Current
                              </Badge>
                            )}
                          </div>
                          <span className="text-sm font-bold text-foreground">+{tier.bonus}%</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {tier.minReferrals === 0
                            ? `0-${tier.maxReferrals} referrals`
                            : tier.maxReferrals === Number.POSITIVE_INFINITY
                              ? `${tier.minReferrals}+ referrals`
                              : `${tier.minReferrals}-${tier.maxReferrals} referrals`}
                        </p>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PanelLayout>
  )
}
